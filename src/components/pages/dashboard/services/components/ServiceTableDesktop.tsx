import React, { useState } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import {
  Service,
  ColumnVisibility,
  ServiceStatus,
  getServiceStatusDisplayText,
} from "../types";
import {
  Trash,
  LoaderCircle,
  Printer,
  Edit,
  Share2,
  Eye,
  Pencil,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { deleteService } from "@/actions/entities/services";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { formatDate } from "@/lib/utils";
import { servicesColumnConfig } from "../config/columnConfig";
import { InlineStatusEditor } from "./InlineStatusEditor";

interface ServiceTableDesktopProps {
  services: Service[];
  columnVisibility: ColumnVisibility;
  handleSort: (field: string) => void;
  getSortIcon: (field: string) => React.ReactNode;
  getStatusBadge: (status: ServiceStatus) => React.ReactNode;
  searchTerm: string;
}

export const ServiceTableDesktop: React.FC<ServiceTableDesktopProps> = ({
  services,
  columnVisibility,
  handleSort,
  getSortIcon,
  getStatusBadge,
  searchTerm,
}) => {
  const router = useRouter();
  const [serviceToDelete, setServiceToDelete] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  // Handle delete service
  const handleDeleteService = async (id: string) => {
    setIsDeleting(true);
    try {
      const result = await deleteService(id);
      if (result.success) {
        toast.success(result.success);
        // Refresh the page to show updated data
        router.refresh();
      } else if (result.error) {
        toast.error(result.error);
      }
    } catch (error) {
      console.error("Error deleting service:", error);
      toast.error("Terjadi kesalahan saat menghapus servis.");
    } finally {
      setIsDeleting(false);
      setServiceToDelete(null);
    }
  };
  if (services.length === 0) {
    return (
      <div className="rounded-md border border-dashed p-8 text-center">
        <h3 className="text-lg font-medium">Tidak ada data servis</h3>
        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
          {searchTerm
            ? `Tidak ada hasil untuk pencarian "${searchTerm}"`
            : "Belum ada data servis yang tersedia. Tambahkan servis baru untuk memulai."}
        </p>
      </div>
    );
  }

  // Helper function to render cell content based on column key
  const renderCellContent = (
    service: Service,
    columnKey: keyof ColumnVisibility
  ) => {
    switch (columnKey) {
      case "serviceNumber":
        return (
          <Link
            href={`/dashboard/services/management/detail/${service.serviceNumber}`}
            className="hover:text-blue-600 text-blue-500 dark:hover:text-blue-400 cursor-pointer underline"
          >
            {service.serviceNumber}
          </Link>
        );
      case "customerName":
        return service.customerName;
      case "customerPhone":
        return service.customerPhone;
      case "deviceType":
        return service.deviceType;
      case "deviceBrand":
        return service.deviceBrand;
      case "deviceModel":
        return service.deviceModel;
      case "deviceSerialNumber":
        return service.deviceSerialNumber || "-";
      case "status":
        return (
          <InlineStatusEditor
            serviceId={service.id}
            currentStatus={service.status}
          />
        );
      case "receivedDate":
        return formatDate(service.receivedDate);
      case "estimatedCompletionDate":
        return service.estimatedCompletionDate
          ? formatDate(service.estimatedCompletionDate)
          : "-";
      case "estimatedCost":
        return service.estimatedCost
          ? `Rp ${service.estimatedCost.toLocaleString("id-ID")}`
          : "-";
      case "finalCost":
        return service.finalCost
          ? `Rp ${service.finalCost.toLocaleString("id-ID")}`
          : "-";
      case "warrantyPeriod":
        return service.warrantyPeriod ? `${service.warrantyPeriod} hari` : "-";
      default:
        return "-";
    }
  };

  // Handle print service (placeholder for future implementation)
  const handlePrintService = (service: Service) => {
    toast.info("Fitur cetak untuk servis akan segera hadir!");
  };

  // Handle share service (placeholder for future implementation)
  const handleShareService = (service: Service) => {
    toast.info("Fitur share untuk servis akan segera hadir!");
  };

  return (
    <div className="relative overflow-x-auto bg-white dark:bg-gray-800 shadow-sm border border-gray-200 dark:border-gray-700 rounded-lg">
      <table className="w-full text-sm text-left text-gray-500 dark:text-gray-400">
        <thead className="sticky top-0 z-10 text-xs text-gray-700 dark:text-gray-300 uppercase bg-white shadow-[0_2px_4px_rgba(0,0,0,0.05)] dark:bg-gray-700">
          <tr>
            {servicesColumnConfig.map(
              (column) =>
                columnVisibility[column.key] && (
                  <th
                    key={column.key}
                    scope="col"
                    className="px-4 py-3 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600 border-r border-gray-200 dark:border-gray-700"
                    onClick={() => handleSort(column.sortKey)}
                  >
                    <div className="flex items-center">
                      {column.label} {getSortIcon(column.sortKey)}
                    </div>
                  </th>
                )
            )}
            <th scope="col" className="px-6 py-3 text-right">
              Aksi
            </th>
          </tr>
        </thead>
        <tbody>
          {services.length > 0 ? (
            services.map((service) => (
              <tr
                key={service.id}
                className="bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 hover:bg-gray-100 dark:hover:bg-gray-700"
              >
                {servicesColumnConfig.map(
                  (column) =>
                    columnVisibility[column.key] && (
                      <td
                        key={column.key}
                        className={`px-4 py-4 whitespace-nowrap border-r border-gray-200 dark:border-gray-700 ${
                          column.key === "serviceNumber"
                            ? "font-medium text-gray-900 dark:text-gray-100"
                            : "text-gray-500 dark:text-gray-400"
                        }`}
                      >
                        {renderCellContent(service, column.key)}
                      </td>
                    )
                )}

                {/* Action Buttons */}
                <td className="px-6 py-4 text-right">
                  <div className="flex items-center justify-end gap-2">
                    {/* Action Button */}
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-8 w-8 bg-gray-500 text-white cursor-pointer hover:bg-gray-400"
                      onClick={() => handlePrintService(service)}
                    >
                      <Printer className="h-4 w-4" />
                      <span className="sr-only">Action</span>
                    </Button>

                    {/* Share Button */}
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-8 w-8 bg-green-500 text-white cursor-pointer hover:bg-green-400"
                      onClick={() => handleShareService(service)}
                    >
                      <Share2 className="h-4 w-4" />
                      <span className="sr-only">Share</span>
                    </Button>

                    {/* Edit Button */}
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-8 w-8 bg-blue-500 text-white cursor-pointer hover:bg-blue-400"
                      onClick={() =>
                        router.push(
                          `/dashboard/services/management/edit/${service.serviceNumber}`
                        )
                      }
                    >
                      <Edit className="h-4 w-4" />
                      <span className="sr-only">Edit</span>
                    </Button>

                    {/* Delete Button */}
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-8 w-8 bg-red-500 text-white cursor-pointer hover:bg-red-400"
                          disabled={
                            isDeleting && serviceToDelete === service.id
                          }
                        >
                          {isDeleting && serviceToDelete === service.id ? (
                            <LoaderCircle className="h-4 w-4 animate-spin" />
                          ) : (
                            <Trash className="h-4 w-4" />
                          )}
                          <span className="sr-only">Delete</span>
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>Hapus Servis</AlertDialogTitle>
                          <AlertDialogDescription>
                            Apakah Anda yakin ingin menghapus servis{" "}
                            <strong>{service.serviceNumber}</strong>? Tindakan
                            ini tidak dapat dibatalkan.
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Batal</AlertDialogCancel>
                          <AlertDialogAction
                            onClick={() => handleDeleteService(service.id)}
                            className="bg-red-600 hover:bg-red-700"
                          >
                            Hapus
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </div>
                </td>
              </tr>
            ))
          ) : (
            <tr>
              <td
                colSpan={servicesColumnConfig.length + 1}
                className="px-6 py-4 text-center text-gray-500 dark:text-gray-400"
              >
                Tidak ada data servis yang tersedia
              </td>
            </tr>
          )}
        </tbody>
      </table>
    </div>
  );
};
